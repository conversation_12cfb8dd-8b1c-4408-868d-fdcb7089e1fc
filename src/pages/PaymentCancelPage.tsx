import { Link, useSearch, useNavigate } from '@tanstack/react-router';
import { useEffect } from 'react';
import { ErrorMessage } from '../components/ui/StatusMessages';

interface PaymentCancelSearchParams {
  error?: string;
}

export const PaymentCancelPage = () => {
  const search = useSearch({ from: '/payment-cancel' }) as PaymentCancelSearchParams;
  const navigate = useNavigate();
  const { error } = search;

  // Function to restore form state when returning from canceled payment
  const restoreFormState = () => {
    const savedState = localStorage.getItem('checkoutFormState');
    if (savedState) {
      try {
        const parsedState = JSON.parse(savedState);
        // Check if the saved state is still valid (e.g., not too old)
        const savedTime = new Date(parsedState.timestamp).getTime();
        const currentTime = new Date().getTime();
        const hourInMs = 60 * 60 * 1000;
        
        // If saved state is less than 1 hour old, it's still valid
        if (currentTime - savedTime < hourInMs) {
          console.log('Restored form state from canceled payment');
        }
      } catch (e) {
        console.error('Error parsing saved form state:', e);
      }
    }
  };

  // Function for programmatic navigation
  const goToZusammenfassung = () => {
    navigate({ to: '/erfassen/zusammenfassung' });
  };

  const goToMyCertificates = () => {
    navigate({ to: '/meine-zertifikate' });
  };

  // Restore form state when component mounts
  useEffect(() => {
    restoreFormState();
    // Clean up checkout session ID as it's no longer needed
    localStorage.removeItem('lastCheckoutSessionId');
  }, []);

  const getErrorMessage = (errorCode?: string) => {
    switch (errorCode) {
      case 'payment_cancelled':
        return 'Sie haben die Zahlung abgebrochen.';
      case 'payment_failed':
        return 'Die Zahlung konnte nicht verarbeitet werden.';
      case 'session_expired':
        return 'Die Zahlungssitzung ist abgelaufen.';
      default:
        return 'Die Zahlung wurde nicht abgeschlossen.';
    }
  };

  return (
    <div className="max-w-2xl mx-auto px-4 py-8">
      <ErrorMessage message={getErrorMessage(error)} type="warning" />

      <div className="bg-white shadow-md rounded-lg p-6 mt-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Zahlung abgebrochen</h1>
        
        <div className="space-y-4">
          <p className="text-gray-600">
            Ihre Zahlung wurde nicht abgeschlossen. Keine Sorge - es wurden keine Gebühren erhoben.
          </p>

          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Was können Sie jetzt tun?</h3>
            <ul className="list-disc list-inside space-y-2 text-gray-600">
              <li>Kehren Sie zur Zusammenfassung zurück und versuchen Sie die Zahlung erneut</li>
              <li>Überprüfen Sie Ihre Zahlungsinformationen</li>
              <li>Kontaktieren Sie uns bei anhaltenden Problemen</li>
            </ul>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="text-md font-medium text-gray-900 mb-2">Häufige Probleme:</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Unzureichende Deckung auf der Karte</li>
              <li>• Falsche Kartendaten eingegeben</li>
              <li>• Karte ist abgelaufen oder gesperrt</li>
              <li>• Internetverbindung unterbrochen</li>
            </ul>
          </div>
        </div>

        <div className="mt-8 pt-6 border-t border-gray-200">
          <div className="flex flex-col sm:flex-row gap-4">
            <button
              onClick={goToZusammenfassung}
              className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
            >
              Zahlung erneut versuchen
            </button>
            <button
              onClick={goToMyCertificates}
              className="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              Zu meinen Zertifikaten
            </button>
            <Link
              to="/"
              className="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              Zur Startseite
            </Link>
          </div>
        </div>

        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <h4 className="text-sm font-medium text-blue-900 mb-2">Benötigen Sie Hilfe?</h4>
          <p className="text-sm text-blue-700">
            Falls Sie weiterhin Probleme haben, kontaktieren Sie uns gerne:
          </p>
          <div className="mt-2 text-sm text-blue-700">
            <p>📧 E-Mail: <EMAIL></p>
            <p>📞 Telefon: +49 (0) 123 456 789</p>
            <p>🕒 Montag - Freitag: 9:00 - 17:00 Uhr</p>
          </div>
        </div>
      </div>
    </div>
  );
};

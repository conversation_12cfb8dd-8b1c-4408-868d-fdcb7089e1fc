import { Link } from '@tanstack/react-router';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '../../lib/supabase';
import { useCertificate } from '../../contexts/CertificateContext';

interface Certificate {
  id: string;
  certificate_type: string | null;
  order_number: string | null;
  created_at: string | null;
  updated_at: string | null;
  status: string | null;
  payment_status: string | null;
}

export const ActiveCertificateIndicator = () => {
  const { activeCertificateId } = useCertificate();

  // Fetch the active certificate data
  const { data: certificate, isLoading } = useQuery({
    queryKey: ['activeCertificate', activeCertificateId],
    queryFn: async () => {
      if (!activeCertificateId) return null;

      const { data, error } = await supabase
        .from('energieausweise')
        .select('id, certificate_type, order_number, created_at, updated_at, status, payment_status')
        .eq('id', activeCertificateId)
        .single();

      if (error) throw error;
      return data as Certificate;
    },
    enabled: !!activeCertificateId,
    retry: false,
  });

  // Helper function to get certificate type display name
  const getCertificateTypeName = (type: string | null) => {
    switch (type) {
      case 'WG/V':
        return 'Wohngebäude-Verbrauchsausweis';
      case 'WG/B':
        return 'Wohngebäude-Bedarfsausweis';
      case 'NWG/V':
        return 'Nicht-Wohngebäude-Verbrauchsausweis';
      default:
        return 'Unbekannter Typ';
    }
  };

  // Helper function to get status display name
  const getStatusName = (status: string | null, paymentStatus: string | null) => {
    if (paymentStatus === 'paid') return 'Bezahlt';
    if (status === 'completed') return 'Abgeschlossen';
    if (status === 'in_progress') return 'In Bearbeitung';
    return 'Unbekannt';
  };

  // Helper function to format date
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Unbekannt';
    return new Date(dateString).toLocaleDateString('de-DE', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  if (isLoading) {
    return (
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <div className="animate-pulse flex space-x-4">
          <div className="rounded-full bg-blue-200 h-10 w-10"></div>
          <div className="flex-1 space-y-2 py-1">
            <div className="h-4 bg-blue-200 rounded w-3/4"></div>
            <div className="h-3 bg-blue-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!certificate) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-yellow-800">
              Kein aktives Zertifikat
            </h3>
            <p className="text-sm text-yellow-700 mt-1">
              Bitte wählen Sie ein Zertifikat aus oder erstellen Sie ein neues.
            </p>
          </div>
          <div className="ml-auto">
            <Link
              to="/meine-zertifikate"
              className="text-yellow-700 hover:text-yellow-900 text-sm font-medium"
            >
              Zertifikate verwalten →
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            <div className="h-10 w-10 bg-green-100 rounded-full flex items-center justify-center">
              <svg className="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-1">
              <h3 className="text-sm font-semibold text-green-900">
                Aktives Zertifikat
              </h3>
              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                {getStatusName(certificate.status, certificate.payment_status)}
              </span>
            </div>
            <p className="text-sm text-green-800 font-medium mb-1">
              {getCertificateTypeName(certificate.certificate_type)}
            </p>
            <div className="flex flex-wrap items-center gap-4 text-xs text-green-700">
              {certificate.order_number && (
                <span className="flex items-center">
                  <svg className="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14" />
                  </svg>
                  {certificate.order_number}
                </span>
              )}
              <span className="flex items-center">
                <svg className="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3a1 1 0 011-1h6a1 1 0 011 1v4m-6 0h6m-6 0V7a1 1 0 00-1 1v9a2 2 0 002 2h4a2 2 0 002-2V8a1 1 0 00-1-1h-1" />
                </svg>
                Erstellt: {formatDate(certificate.created_at)}
              </span>
              <span className="flex items-center">
                <svg className="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                Zuletzt bearbeitet: {formatDate(certificate.updated_at)}
              </span>
            </div>
          </div>
        </div>
        <div className="flex-shrink-0 ml-4">
          <Link
            to="/meine-zertifikate"
            className="inline-flex items-center px-3 py-1.5 border border-green-300 text-xs font-medium rounded text-green-700 bg-white hover:bg-green-50 transition-colors"
          >
            <svg className="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
            </svg>
            Wechseln
          </Link>
        </div>
      </div>
    </div>
  );
};

import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '../../lib/supabase';
import { LoadingSpinner, ErrorMessage } from '../ui/StatusMessages';

interface EmailLog {
  id: string;
  certificate_id: string;
  recipient_email: string;
  email_type: 'customer_success' | 'customer_failure' | 'admin_success' | 'admin_failure';
  sent_at: string | null;
  status: 'sent' | 'failed' | 'pending';
  error_message: string | null;
  resend_message_id: string | null;
  created_at: string;
  updated_at: string;
  // Joined data from energieausweise table
  order_number?: string;
  certificate_type?: string;
}

const EMAIL_TYPE_LABELS = {
  customer_success: 'Kunde - Erfolg',
  customer_failure: 'Kunde - Fehlschlag',
  admin_success: 'Admin - Erfolg',
  admin_failure: 'Admin - Fehlschlag'
};

const STATUS_LABELS = {
  sent: 'Gesendet',
  failed: 'Fehlgeschlagen',
  pending: 'Ausstehend'
};

const STATUS_COLORS = {
  sent: 'text-green-600 bg-green-100',
  failed: 'text-red-600 bg-red-100',
  pending: 'text-yellow-600 bg-yellow-100'
};

export const EmailLogsView = () => {
  const [selectedEmailType, setSelectedEmailType] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');

  // Fetch email logs with certificate data
  const { data: emailLogs, isLoading, error, refetch } = useQuery({
    queryKey: ['emailLogs', selectedEmailType, selectedStatus],
    queryFn: async () => {
      let query = supabase
        .from('email_logs')
        .select(`
          *,
          energieausweise!inner(
            order_number,
            certificate_type
          )
        `)
        .order('created_at', { ascending: false });

      // Apply filters
      if (selectedEmailType !== 'all') {
        query = query.eq('email_type', selectedEmailType);
      }
      if (selectedStatus !== 'all') {
        query = query.eq('status', selectedStatus);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching email logs:', error);
        throw error;
      }

      // Flatten the joined data
      return data?.map(log => ({
        ...log,
        order_number: log.energieausweise?.order_number,
        certificate_type: log.energieausweise?.certificate_type
      })) || [];
    },
    staleTime: 30 * 1000, // 30 seconds
  });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('de-DE', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="py-4">
        <ErrorMessage message="Fehler beim Laden der E-Mail-Logs." />
        <button
          onClick={() => refetch()}
          className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Erneut versuchen
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">E-Mail-Logs</h2>
        <button
          onClick={() => refetch()}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Aktualisieren
        </button>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow space-y-4">
        <h3 className="text-lg font-medium text-gray-900">Filter</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="emailType" className="block text-sm font-medium text-gray-700 mb-1">
              E-Mail-Typ
            </label>
            <select
              id="emailType"
              value={selectedEmailType}
              onChange={(e) => setSelectedEmailType(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">Alle Typen</option>
              <option value="customer_success">Kunde - Erfolg</option>
              <option value="customer_failure">Kunde - Fehlschlag</option>
              <option value="admin_success">Admin - Erfolg</option>
              <option value="admin_failure">Admin - Fehlschlag</option>
            </select>
          </div>
          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              id="status"
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">Alle Status</option>
              <option value="sent">Gesendet</option>
              <option value="failed">Fehlgeschlagen</option>
              <option value="pending">Ausstehend</option>
            </select>
          </div>
        </div>
      </div>

      {/* Email logs table */}
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        {emailLogs && emailLogs.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Bestellnummer
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    E-Mail-Typ
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Empfänger
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Gesendet am
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Erstellt am
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Fehler
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {emailLogs.map((log) => (
                  <tr key={log.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {log.order_number || 'N/A'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {EMAIL_TYPE_LABELS[log.email_type]}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {log.recipient_email}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${STATUS_COLORS[log.status]}`}>
                        {STATUS_LABELS[log.status]}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {log.sent_at ? formatDate(log.sent_at) : '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatDate(log.created_at)}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                      {log.error_message ? (
                        <span className="text-red-600" title={log.error_message}>
                          {log.error_message}
                        </span>
                      ) : (
                        '-'
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500">Keine E-Mail-Logs gefunden.</p>
          </div>
        )}
      </div>

      {/* Summary statistics */}
      {emailLogs && emailLogs.length > 0 && (
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Statistiken</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {emailLogs.filter(log => log.status === 'sent').length}
              </div>
              <div className="text-sm text-gray-500">Erfolgreich gesendet</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {emailLogs.filter(log => log.status === 'failed').length}
              </div>
              <div className="text-sm text-gray-500">Fehlgeschlagen</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">
                {emailLogs.filter(log => log.status === 'pending').length}
              </div>
              <div className="text-sm text-gray-500">Ausstehend</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import Stripe from "https://esm.sh/stripe@12.18.0";

// CORS headers to allow cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS'
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { 
      status: 204, 
      headers: corsHeaders 
    });
  }

  try {
    const stripeSecretKey = Deno.env.get("STRIPE_SECRET_KEY");
    if (!stripeSecretKey) {
      throw new Error("STRIPE_SECRET_KEY environment variable is not set");
    }

    const stripe = new Stripe(stripeSecretKey);
    
    // Parse the request body
    const { amount, successUrl, cancelUrl, paymentId, userEmail, orderNumber } = await req.json();
    
    if (!amount || !successUrl || !cancelUrl) {
      throw new Error("Missing required parameters: amount, successUrl, cancelUrl");
    }

    console.log("Creating checkout session with params:", { 
      amount, 
      successUrl, 
      cancelUrl, 
      paymentId, 
      userEmail, 
      orderNumber 
    });

    // Create a Stripe checkout session
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'eur',
            product_data: {
              name: 'Energieausweis',
              description: 'Ihr personalisierter Energieausweis nach aktuellen Standards',
              images: [], // You can add product images here if needed
            },
            unit_amount: amount, // amount in cents
          },
          quantity: 1,
        },
      ],
      mode: 'payment',
      success_url: successUrl,
      cancel_url: cancelUrl,
      client_reference_id: paymentId, // Store the certificate ID for the webhook
      customer_email: userEmail,
      metadata: {
        order_number: orderNumber,
        certificate_id: paymentId,
      },
      // Additional settings for better UX
      billing_address_collection: 'auto',
      shipping_address_collection: {
        allowed_countries: ['DE', 'AT', 'CH'], // Limit to DACH region
      },
      payment_intent_data: {
        metadata: {
          order_number: orderNumber,
          certificate_id: paymentId,
        },
      },
      // Customize the checkout experience
      locale: 'de', // German locale
      allow_promotion_codes: false, // Disable promotion codes for now
    });

    console.log("Checkout session created:", session.id);

    return new Response(
      JSON.stringify({ 
        id: session.id, 
        url: session.url,
        payment_status: session.payment_status 
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  } catch (error) {
    console.error(`Error creating checkout session: ${error.message}`);
    
    // Determine error code for better client-side handling
    let errorCode = 'session_creation_failed';
    let status = 500;
    
    if (error.message.includes('authentication')) {
      errorCode = 'authentication_error';
      status = 401;
    } else if (error.message.includes('parameter')) {
      errorCode = 'invalid_parameters';
      status = 400;
    } else if (error.message.includes('Stripe')) {
      errorCode = 'stripe_error';
    }
    
    return new Response(
      JSON.stringify({ 
        error: error.message,
        code: errorCode
      }),
      { 
        status: status, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
});
